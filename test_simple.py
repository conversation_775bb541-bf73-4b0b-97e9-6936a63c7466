#!/usr/bin/env python3
"""
Simple test to verify the enhanced PDF generation works.
"""

import asyncio
from src.v2.KB.kb_setup.handle_url import save_pdf

async def test_simple():
    """Test with a simple URL first."""
    print("🧪 Testing Enhanced PDF Generation")
    print("=" * 50)
    
    test_url = "https://www.divinecosmeticsurgery.com/"
    
    try:
        print(f"📄 Generating PDF from: {test_url}")
        pdf_file = await save_pdf(test_url)
        
        pdf_bytes = await pdf_file.read()
        print(f"✅ PDF generated: {len(pdf_bytes)} bytes")
        
        # Save to disk
        with open(f"test_enhanced_{pdf_file.filename}", "wb") as f:
            f.write(pdf_bytes)
        
        print(f"💾 Saved as: test_enhanced_{pdf_file.filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_simple())
