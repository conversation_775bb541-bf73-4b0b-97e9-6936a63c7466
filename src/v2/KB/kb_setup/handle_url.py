import re
import asyncio
import httpx
import pdfkit
from selectolax.parser import HTMLParser
from fastapi import UploadFile
from io import BytesIO
async def get_filename(title):
    filename = title.lower().replace(" ", "_").replace("/", "_").replace("|", "_")
    filename = re.sub(r'[^a-zA-Z0-9_\-]+', '', filename)

    # Add leading zeros to make 4-digit filename
    if filename.isdigit():
        filename = f"{int(filename):04d}"
    else:
        filename = f"{filename.zfill(4)}"

    return filename


async def generate_pdf_from_html(html_content: str) -> bytes:
    """
    Generate PDF from HTML using pdfkit with optimized settings for readability.
    Configured for both CentOS and Ubuntu compatibility.
    """
    # Enhanced pdfkit options for maximum readability and compatibility
    options = {
        'page-size': 'A4',
        'margin-top': '1in',
        'margin-right': '0.8in',
        'margin-bottom': '1in',
        'margin-left': '0.8in',
        'encoding': "UTF-8",
        'no-outline': None,
        'enable-local-file-access': None,
        'print-media-type': None,
        'disable-smart-shrinking': None,
        'minimum-font-size': '14',  # Larger font for better readability
        'dpi': 300,
        'image-quality': 95,
        'image-dpi': 300,
        'enable-javascript': None,
        'javascript-delay': 2000,
        'load-error-handling': 'ignore',
        'load-media-error-handling': 'ignore',
        'disable-plugins': None,
        'disable-external-links': None,
        'quiet': None
    }

    try:
        # Run pdfkit in a thread to avoid blocking
        pdf_bytes = await asyncio.to_thread(
            pdfkit.from_string,
            html_content,
            False,
            options=options
        )
        return pdf_bytes
    except Exception as e:
        print(f"PDF generation with full options failed: {e}, trying with minimal options...")
        # Fallback with minimal options for maximum compatibility
        minimal_options = {
            'page-size': 'A4',
            'encoding': "UTF-8",
            'enable-local-file-access': None,
            'quiet': None
        }
        try:
            pdf_bytes = await asyncio.to_thread(
                pdfkit.from_string,
                html_content,
                False,
                options=minimal_options
            )
            return pdf_bytes
        except Exception as fallback_error:
            print(f"Even minimal PDF generation failed: {fallback_error}")
            raise

async def save_pdf(url: str, filename: str = None):
    """
    Enhanced PDF generation with JavaScript handling and content optimization.
    Uses multiple strategies to capture dynamic content while staying lightweight.
    """
    try:
        # Strategy 1: Try to get rendered content with JavaScript simulation
        html_content = await fetch_with_js_simulation(url)

        # Parse with selectolax for fast HTML processing
        tree = HTMLParser(html_content)

        # Extract title for filename
        title_element = tree.css_first('title')
        title = title_element.text() if title_element else "untitled"
        output_filename = await get_filename(filename or title)

        # Enhanced content processing for JavaScript-heavy sites
        cleaned_html = await process_dynamic_content(tree, url)

        # Generate PDF using pdfkit with optimized settings for readability
        pdf_bytes = await generate_pdf_from_html(cleaned_html)

        # Create filename with website suffix
        final_filename = f"{output_filename}_website.pdf"

        print(f"PDF generated for {url}")

        return UploadFile(
            filename=final_filename,
            file=BytesIO(pdf_bytes),
            headers={"Content-Type": "application/pdf"}
        )

    except Exception as e:
        print(f"Error generating PDF from {url}: {e}")
        raise


async def fetch_with_js_simulation(url: str) -> str:
    """
    Fetch HTML with JavaScript simulation using lightweight approach.
    Tries multiple strategies to get the most complete content.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    async with httpx.AsyncClient(
        timeout=45.0,
        headers=headers,
        follow_redirects=True
    ) as client:
        # First, get the initial HTML
        response = await client.get(url)
        html_content = response.text

        # Try to detect and fetch additional content that might be loaded via AJAX
        tree = HTMLParser(html_content)

        # Look for common patterns that indicate dynamic content
        await simulate_common_js_patterns(tree, client, url)

        return tree.html


async def simulate_common_js_patterns(tree: HTMLParser, client: httpx.AsyncClient, base_url: str):
    """
    Simulate common JavaScript patterns to reveal hidden content.
    This is a lightweight alternative to full browser automation.
    """
    # Look for lazy-loaded images and try to load them
    for img in tree.css('img[data-src], img[data-lazy]'):
        data_src = img.attributes.get('data-src') or img.attributes.get('data-lazy')
        if data_src:
            img.attributes['src'] = data_src

    # Look for hidden content that might be shown by JavaScript
    for element in tree.css('[style*="display:none"], [style*="display: none"], .hidden'):
        if element.attributes and 'style' in element.attributes:
            style = element.attributes['style']
            # Remove display:none to make content visible
            style = style.replace('display:none', '').replace('display: none', '')
            element.attributes['style'] = style

    # Remove hidden class
    for element in tree.css('.hidden'):
        if element.attributes and 'class' in element.attributes:
            classes = element.attributes['class'].split()
            classes = [c for c in classes if c != 'hidden']
            element.attributes['class'] = ' '.join(classes)


async def process_dynamic_content(tree: HTMLParser, url: str) -> str:
    """
    Process dynamic content and create a comprehensive PDF-ready HTML.
    Handles sliders, tabs, accordions, and other interactive elements.
    """
    # Remove problematic scripts and styles first
    for element in tree.css('script, noscript'):
        element.decompose()

    # Remove external stylesheets that might interfere
    for element in tree.css('link[rel="stylesheet"]'):
        element.decompose()

    # Handle navigation and remove non-content elements
    nav_selectors = [
        'nav', 'header', '.navbar', '.nav', '.navigation',
        '.header', '.top-bar', '.menu', '.sidebar', '.breadcrumb'
    ]
    for selector in nav_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Remove footer elements
    for element in tree.css('footer, .footer, .copyright, .footer-content'):
        element.decompose()

    # Handle sliders and carousels - make all slides visible
    slider_selectors = [
        '.slick-slider', '.swiper-container', '.owl-carousel', '.carousel',
        '.slider', '.slideshow', '.gallery-slider'
    ]

    for selector in slider_selectors:
        for container in tree.css(selector):
            # Find all slides within this container
            slide_selectors = [
                '.slick-slide', '.swiper-slide', '.owl-item', '.carousel-item',
                '.slide', '.gallery-item'
            ]

            for slide_selector in slide_selectors:
                for slide in container.css(slide_selector):
                    # Remove any hiding classes or styles
                    if slide.attributes:
                        # Remove problematic classes
                        if 'class' in slide.attributes:
                            classes = slide.attributes['class'].split()
                            classes = [c for c in classes if not any(hide in c.lower() for hide in ['hidden', 'inactive', 'fade'])]
                            slide.attributes['class'] = ' '.join(classes)

                        # Remove hiding styles
                        if 'style' in slide.attributes:
                            style = slide.attributes['style']
                            style = style.replace('display:none', '').replace('display: none', '')
                            style = style.replace('visibility:hidden', '').replace('visibility: hidden', '')
                            slide.attributes['style'] = style

    # Handle tabs and accordions - make all content visible
    tab_selectors = ['.tab-content', '.accordion-content', '.collapse', '.panel-collapse']
    for selector in tab_selectors:
        for element in tree.css(selector):
            if element.attributes:
                if 'class' in element.attributes:
                    classes = element.attributes['class'].split()
                    classes = [c for c in classes if not any(hide in c.lower() for hide in ['hidden', 'collapse', 'fade'])]
                    classes.append('show')  # Bootstrap show class
                    element.attributes['class'] = ' '.join(classes)

    # Remove modal and popup elements that might interfere
    modal_selectors = [
        '.modal', '.popup', '.overlay', '[role="dialog"]',
        '[aria-modal="true"]', '.lightbox', '.fancybox'
    ]
    for selector in modal_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Clean up problematic attributes
    for element in tree.css('*'):
        if element.attributes:
            # Remove event handlers and data attributes that might cause issues
            attrs_to_remove = []
            for attr in element.attributes:
                if attr.startswith(('on', 'data-')) and attr not in ['data-src', 'data-alt']:
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                element.attributes.pop(attr, None)

    # Get the processed HTML and add comprehensive CSS
    html_content = tree.html

    # Add enhanced CSS for perfect PDF rendering
    enhanced_css = """
    <style>
    @page {
        margin: 0.8in;
        size: A4;
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-size: 12px;
        line-height: 1.6;
        color: #000000;
        background-color: #ffffff;
        margin: 0;
        padding: 15px;
        text-align: left;
    }

    /* Ensure all content is visible */
    .hidden, .d-none, .invisible {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Slider and carousel fixes */
    .slick-slider, .swiper-container, .owl-carousel, .carousel,
    .slider, .slideshow, .gallery-slider {
        overflow: visible !important;
        height: auto !important;
    }

    .slick-slide, .swiper-slide, .owl-item, .carousel-item,
    .slide, .gallery-item {
        display: block !important;
        opacity: 1 !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        transform: none !important;
        margin-bottom: 15px;
        border: 1px solid #eee;
        padding: 10px;
        page-break-inside: avoid;
    }

    /* Tab and accordion content */
    .tab-content, .accordion-content, .collapse, .panel-collapse {
        display: block !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
        margin-bottom: 15px;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Arial', sans-serif;
        color: #000000;
        margin-top: 20px;
        margin-bottom: 10px;
        page-break-after: avoid;
        font-weight: bold;
    }

    h1 { font-size: 20px; }
    h2 { font-size: 18px; }
    h3 { font-size: 16px; }
    h4 { font-size: 14px; }
    h5 { font-size: 12px; }
    h6 { font-size: 11px; }

    p {
        margin-bottom: 10px;
        orphans: 3;
        widows: 3;
    }

    /* Images */
    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 10px auto;
        page-break-inside: avoid;
    }

    /* Tables */
    table {
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
        font-size: 10px;
        page-break-inside: avoid;
    }

    th, td {
        border: 1px solid #000000;
        padding: 5px;
        text-align: left;
        vertical-align: top;
    }

    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    /* Lists */
    ul, ol {
        margin: 10px 0;
        padding-left: 25px;
    }

    li {
        margin-bottom: 5px;
    }

    /* Links */
    a {
        color: #000000;
        text-decoration: underline;
    }

    /* Remove problematic elements */
    .advertisement, .ads, .popup, .modal, .overlay,
    .cookie-notice, .newsletter-popup, .social-share {
        display: none !important;
    }

    /* Ensure content sections are visible */
    .content, .main-content, .article, .post-content,
    .entry-content, .page-content {
        display: block !important;
        visibility: visible !important;
    }

    /* Page break controls */
    .page-break {
        page-break-before: always;
    }

    .no-break {
        page-break-inside: avoid;
    }

    /* Responsive elements */
    .container, .row, .col, [class*="col-"] {
        width: 100% !important;
        float: none !important;
        display: block !important;
    }
    </style>
    """

    # Insert CSS into head
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>{enhanced_css}')
    elif '<html>' in html_content:
        html_content = html_content.replace('<html>', f'<html><head>{enhanced_css}</head>')
    else:
        html_content = f'<html><head>{enhanced_css}</head><body>{html_content}</body></html>'

    return html_content


async def clean_html_for_pdf(tree: HTMLParser) -> str:
    """
    Clean HTML content for better PDF generation.
    Removes problematic elements while preserving content structure.
    """
    # Remove scripts, styles, and other non-content elements
    for element in tree.css('script, style, noscript'):
        element.decompose()

    # Remove navigation and header elements that might interfere with PDF layout
    nav_selectors = [
        'nav', 'header', '.navbar', '.nav', '.navigation',
        '.header', '.top-bar', '.menu', '.sidebar'
    ]
    for selector in nav_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Remove footer elements
    for element in tree.css('footer, .footer, .copyright'):
        element.decompose()

    # Remove modal and popup elements
    modal_selectors = [
        '.modal', '.popup', '.overlay', '[role="dialog"]',
        '[aria-modal="true"]', '.lightbox'
    ]
    for selector in modal_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Fix slider/carousel elements for PDF (make all slides visible)
    slider_selectors = [
        '.slick-slider', '.swiper-container', '.owl-carousel', '.carousel'
    ]
    for selector in slider_selectors:
        for container in tree.css(selector):
            # Remove slider-specific classes that hide content
            if container.attributes:
                container.attributes.pop('class', None)

            # Make all slides visible
            slides = container.css('.slick-slide, .swiper-slide, .owl-item, .carousel-item')
            for slide in slides:
                if slide.attributes:
                    slide.attributes.pop('style', None)
                    slide.attributes.pop('class', None)

    # Remove problematic attributes that can break PDF generation
    for element in tree.css('*'):
        if element.attributes:
            # Remove event handlers and data attributes
            attrs_to_remove = []
            for attr in element.attributes:
                if attr.startswith(('on', 'data-')):
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                element.attributes.pop(attr, None)

    # Get the HTML and add enhanced CSS for better PDF formatting and readability
    html_content = tree.html

    # Enhanced CSS for maximum readability in PDFs
    css_style = """
    <style>
    @page {
        margin: 1in;
        size: A4;
    }

    body {
        font-family: 'Times New Roman', Times, serif;
        font-size: 14px;
        line-height: 1.8;
        color: #000000;
        background-color: #ffffff;
        margin: 0;
        padding: 20px;
        text-align: justify;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: Arial, sans-serif;
        color: #000000;
        margin-top: 24px;
        margin-bottom: 12px;
        page-break-after: avoid;
    }

    h1 { font-size: 24px; font-weight: bold; }
    h2 { font-size: 20px; font-weight: bold; }
    h3 { font-size: 18px; font-weight: bold; }
    h4 { font-size: 16px; font-weight: bold; }

    p {
        margin-bottom: 12px;
        text-indent: 0;
        orphans: 3;
        widows: 3;
    }

    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 12px auto;
        page-break-inside: avoid;
    }

    table {
        border-collapse: collapse;
        width: 100%;
        margin: 12px 0;
        font-size: 12px;
        page-break-inside: avoid;
    }

    th, td {
        border: 1px solid #000000;
        padding: 8px;
        text-align: left;
        vertical-align: top;
    }

    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    ul, ol {
        margin: 12px 0;
        padding-left: 30px;
    }

    li {
        margin-bottom: 6px;
    }

    a {
        color: #000000;
        text-decoration: underline;
    }

    blockquote {
        margin: 12px 0;
        padding: 12px;
        border-left: 4px solid #cccccc;
        background-color: #f9f9f9;
        font-style: italic;
    }

    code, pre {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 4px;
        border: 1px solid #cccccc;
    }

    pre {
        padding: 12px;
        margin: 12px 0;
        white-space: pre-wrap;
        page-break-inside: avoid;
    }

    /* Ensure slider content is visible */
    .slick-slide, .swiper-slide, .owl-item, .carousel-item {
        display: block !important;
        opacity: 1 !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        transform: none !important;
    }

    /* Hide problematic elements */
    .advertisement, .ads, .popup, .modal, .overlay {
        display: none !important;
    }

    /* Page break controls */
    .page-break {
        page-break-before: always;
    }

    .no-break {
        page-break-inside: avoid;
    }
    </style>
    """

    # Insert CSS into head using string manipulation
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>{css_style}')
    elif '<html>' in html_content:
        html_content = html_content.replace('<html>', f'<html><head>{css_style}</head>')
    else:
        # If no html tag, wrap everything
        html_content = f'<html><head>{css_style}</head><body>{html_content}</body></html>'

    return html_content


async def handle_urls(urls: list) -> list[UploadFile]:
    """
    Process multiple URLs in parallel to generate PDFs.
    Optimized for speed with lightweight HTTP requests.
    """
    if not urls:
        return []

    tasks = []
    for url in urls:
        tasks.append(save_pdf(url))

    # Process all URLs concurrently for maximum speed
    pdfs = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out exceptions and return only successful PDFs
    successful_pdfs = []
    for i, result in enumerate(pdfs):
        if isinstance(result, Exception):
            print(f"Failed to process URL {urls[i]}: {result}")
        else:
            successful_pdfs.append(result)

    return successful_pdfs

if __name__ == "__main__":
    import asyncio
    asyncio.run(save_pdf("https://www.divinecosmeticsurgery.com/liposuction-surgery-cost.php"))