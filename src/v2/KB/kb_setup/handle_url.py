import re
import async<PERSON>
import httpx
from selectolax.parser import HTMLParser
from fastapi import UploadFile
from io import BytesIO
from weasyprint import HTML
async def get_filename(title):
    filename = title.lower().replace(" ", "_").replace("/", "_").replace("|", "_")
    filename = re.sub(r'[^a-zA-Z0-9_\-]+', '', filename)

    # Add leading zeros to make 4-digit filename
    if filename.isdigit():
        filename = f"{int(filename):04d}"
    else:
        filename = f"{filename.zfill(4)}"

    return filename

async def save_pdf(url: str, filename: str = None):
    """
    Lightweight PDF generation from URL using httpx + selectolax + weasyprint.
    Much faster than <PERSON><PERSON> while maintaining the same PDF output quality.
    """
    try:
        # Fetch HTML content with httpx (much faster than browser automation)
        async with httpx.AsyncClient(
            timeout=30.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        ) as client:
            response = await client.get(url)
            html_content = response.text

        # Parse with selectolax for fast HTML processing
        tree = HTMLParser(html_content)

        # Extract title for filename
        title_element = tree.css_first('title')
        title = title_element.text() if title_element else "untitled"
        output_filename = await get_filename(filename or title)

        # Clean HTML for better PDF generation (similar to original logic)
        cleaned_html = await clean_html_for_pdf(tree)

        # Generate PDF using weasyprint (same as original makepdf function)
        pdf_bytes = HTML(string=cleaned_html, base_url=url).write_pdf()

        # Create filename with website suffix
        final_filename = f"{output_filename}_website.pdf"

        print(f"PDF generated for {url}")

        return UploadFile(
            filename=final_filename,
            file=BytesIO(pdf_bytes),
            headers={"Content-Type": "application/pdf"}
        )

    except Exception as e:
        print(f"Error generating PDF from {url}: {e}")
        raise
async def clean_html_for_pdf(tree: HTMLParser) -> str:
    """
    Clean HTML content for better PDF generation.
    Removes problematic elements while preserving content structure.
    """
    # Remove scripts, styles, and other non-content elements
    for element in tree.css('script, style, noscript'):
        element.decompose()

    # Remove navigation and header elements that might interfere with PDF layout
    nav_selectors = [
        'nav', 'header', '.navbar', '.nav', '.navigation',
        '.header', '.top-bar', '.menu', '.sidebar'
    ]
    for selector in nav_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Remove footer elements
    for element in tree.css('footer, .footer, .copyright'):
        element.decompose()

    # Remove modal and popup elements
    modal_selectors = [
        '.modal', '.popup', '.overlay', '[role="dialog"]',
        '[aria-modal="true"]', '.lightbox'
    ]
    for selector in modal_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Fix slider/carousel elements for PDF (make all slides visible)
    slider_selectors = [
        '.slick-slider', '.swiper-container', '.owl-carousel', '.carousel'
    ]
    for selector in slider_selectors:
        for container in tree.css(selector):
            # Remove slider-specific classes that hide content
            if container.attributes:
                container.attributes.pop('class', None)

            # Make all slides visible
            slides = container.css('.slick-slide, .swiper-slide, .owl-item, .carousel-item')
            for slide in slides:
                if slide.attributes:
                    slide.attributes.pop('style', None)
                    slide.attributes.pop('class', None)

    # Remove problematic attributes that can break PDF generation
    for element in tree.css('*'):
        if element.attributes:
            # Remove event handlers and data attributes
            attrs_to_remove = []
            for attr in element.attributes:
                if attr.startswith(('on', 'data-')):
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                element.attributes.pop(attr, None)

    # Get the HTML and add basic CSS for better PDF formatting
    html_content = tree.html

    css_style = """
    <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
    img { max-width: 100%; height: auto; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .slick-slide, .swiper-slide, .owl-item, .carousel-item {
        display: block !important;
        opacity: 1 !important;
        position: static !important;
    }
    </style>
    """

    # Insert CSS into head using string manipulation
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>{css_style}')
    elif '<html>' in html_content:
        html_content = html_content.replace('<html>', f'<html><head>{css_style}</head>')
    else:
        # If no html tag, wrap everything
        html_content = f'<html><head>{css_style}</head><body>{html_content}</body></html>'

    return html_content


async def handle_urls(urls: list) -> list[UploadFile]:
    """
    Process multiple URLs in parallel to generate PDFs.
    Optimized for speed with lightweight HTTP requests.
    """
    if not urls:
        return []

    tasks = []
    for url in urls:
        tasks.append(save_pdf(url))

    # Process all URLs concurrently for maximum speed
    pdfs = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out exceptions and return only successful PDFs
    successful_pdfs = []
    for i, result in enumerate(pdfs):
        if isinstance(result, Exception):
            print(f"Failed to process URL {urls[i]}: {result}")
        else:
            successful_pdfs.append(result)

    return successful_pdfs

if __name__ == "__main__":
    import asyncio
    asyncio.run(save_pdf("https://www.divinecosmeticsurgery.com/liposuction-surgery-cost.php"))