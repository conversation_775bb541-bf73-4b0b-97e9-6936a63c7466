#!/usr/bin/env python3
"""
Test script specifically for https://www.divinecosmeticsurgery.com/
"""

import asyncio
import fitz  # PyMuPDF for reading PDFs
from src.v2.KB.kb_setup.handle_url import save_pdf

async def test_divine_cosmetic_url():
    """Test the specific URL that was mentioned."""
    print("🧪 Testing Divine Cosmetic Surgery Website")
    print("=" * 60)
    
    test_url = "https://www.divinecosmeticsurgery.com/"
    
    try:
        # Generate PDF
        print(f"📄 Generating PDF from: {test_url}")
        pdf_file = await save_pdf(test_url)
        
        # Read the PDF content
        pdf_bytes = await pdf_file.read()
        print(f"✅ PDF generated successfully: {len(pdf_bytes)} bytes")
        print(f"📁 Filename: {pdf_file.filename}")
        
        # Test PDF readability using PyMuPDF
        doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        print(f"📖 PDF has {len(doc)} page(s)")
        
        # Extract text from all pages
        all_text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            page_text = page.get_text()
            all_text += page_text
            print(f"📄 Page {page_num + 1}: {len(page_text)} characters")
            
            # Show first few lines of each page
            lines = page_text.split('\n')[:5]
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:80]}...")
        
        doc.close()
        
        # Check if text was extracted
        if all_text.strip():
            print(f"\n✅ PDF is readable! Total extracted: {len(all_text)} characters")
            
            # Show some sample content
            words = all_text.split()
            print(f"📝 Word count: {len(words)}")
            print(f"📝 Sample content (first 300 chars):")
            print(f"   {all_text[:300]}...")
            
            # Check for common website elements
            content_checks = {
                "Has title/heading": any(word.lower() in ['divine', 'cosmetic', 'surgery'] for word in words[:20]),
                "Has substantial content": len(words) > 50,
                "Has contact info": any(word.lower() in ['contact', 'phone', 'email'] for word in words),
                "Has navigation": any(word.lower() in ['home', 'about', 'services'] for word in words)
            }
            
            print(f"\n📊 Content Analysis:")
            for check, result in content_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check}")
            
            return True
        else:
            print("❌ PDF appears to be empty or unreadable")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    success = await test_divine_cosmetic_url()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test PASSED! The website PDF is readable and contains content.")
    else:
        print("⚠️  Test FAILED! Check the implementation or website accessibility.")

if __name__ == "__main__":
    asyncio.run(main())
