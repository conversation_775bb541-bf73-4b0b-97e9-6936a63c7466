"""
Optimized FastAPI application with immediate startup and background initialization.
"""

from fastapi import FastAPI, BackgroundTasks
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

from src.helper.logger import setup_new_logging
from src.core.activity_log import UserActivityMiddleware
from src.core.exceptions import add_exception_handlers
from main_routes import include_routers
from src.v3 import v3_app

# Initialize logger
loggers = setup_new_logging(__name__)
loggers.info("Application starting with optimized startup")

# Global state for tracking initialization
app_state = {
    "startup_complete": False,
    "nltk_ready": False,
    "background_init_complete": False,
    "startup_time": None,
    "errors": []
}

async def quick_nltk_check():
    """Quick check if NLTK data is available, don't download if missing."""
    try:
        import nltk
        from nltk.data import find
        
        # Set NLTK data path
        nltk_data_path = os.environ.get('NLTK_DATA', '/app/nltk_data')
        if nltk_data_path not in nltk.data.path:
            nltk.data.path.insert(0, nltk_data_path)
        
        # Quick check for essential packages
        essential_packages = ['punkt', 'wordnet', 'stopwords']
        for package in essential_packages:
            try:
                if package == 'punkt':
                    find('tokenizers/punkt')
                else:
                    find(f'corpora/{package}')
            except LookupError:
                loggers.warning(f"NLTK {package} not found, will work without it")
                return False
        
        app_state["nltk_ready"] = True
        loggers.info("NLTK data check completed successfully")
        return True
    except Exception as e:
        loggers.warning(f"NLTK check failed: {e}, continuing without NLTK")
        app_state["errors"].append(f"NLTK: {str(e)}")
        return False

async def background_initialization():
    """Run heavy initialization tasks in the background."""
    try:
        loggers.info("Starting background initialization")
        
        # Only download NLTK if not available and needed
        if not app_state["nltk_ready"]:
            try:
                from main_routes import nltk_download
                await nltk_download()
                app_state["nltk_ready"] = True
                loggers.info("Background NLTK download completed")
            except Exception as e:
                loggers.warning(f"Background NLTK download failed: {e}")
                app_state["errors"].append(f"NLTK Download: {str(e)}")
        
        # Add other heavy initialization tasks here
        # Example: Pre-warm AI models, cache configurations, etc.
        
        app_state["background_init_complete"] = True
        loggers.info("Background initialization completed")
        
    except Exception as e:
        loggers.error(f"Background initialization failed: {e}")
        app_state["errors"].append(f"Background Init: {str(e)}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Optimized lifespan with immediate startup and background initialization.
    """
    import time
    start_time = time.time()
    
    # Quick essential checks only
    loggers.info("Running quick startup checks")
    await quick_nltk_check()
    
    # Mark startup as complete immediately
    app_state["startup_complete"] = True
    app_state["startup_time"] = time.time() - start_time
    loggers.info(f"Quick startup completed in {app_state['startup_time']:.2f}s")
    
    # Start background initialization (non-blocking)
    asyncio.create_task(background_initialization())
    
    # Yield control back to FastAPI immediately
    yield
    
    # Shutdown
    loggers.info("Application shutting down")

# Create FastAPI application with optimized lifespan
app = FastAPI(
    title="Eko Backend",
    description="Eko AI API - Optimized",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware with optimized settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],  # Specific methods only
    allow_headers=["*"],
)

# Add user activity tracking middleware with exclusions
app.add_middleware(
    UserActivityMiddleware,
    exclude_paths=["/docs", "/openapi.json", "/redoc", "/health", "/health-minimal", "/health-detailed"],
    auto_error=False
)

# Include all routers
include_routers(app)

# Mount v3 API as a sub-application
app.mount("/v3", v3_app)

# Add exception handlers
add_exception_handlers(app)

# Root endpoint redirects to API documentation
@app.get("/")
async def root():
    return RedirectResponse(url="/docs")

# Minimal health check for load balancer (ultra-fast)
@app.get("/health-minimal")
async def health_minimal():
    """Ultra-fast health check for load balancer."""
    return {"status": "ok"}

# Standard health check
@app.get("/health")
async def health_check():
    """Standard health check endpoint."""
    return {
        "status": "healthy",
        "service": "eko-backend",
        "version": "0.1.0",
        "startup_complete": app_state["startup_complete"]
    }

# Detailed health check with initialization status
@app.get("/health-detailed")
async def health_detailed():
    """Detailed health check with initialization status."""
    return {
        "status": "healthy" if app_state["startup_complete"] else "starting",
        "service": "eko-backend",
        "version": "0.1.0",
        "initialization": {
            "startup_complete": app_state["startup_complete"],
            "startup_time": app_state["startup_time"],
            "nltk_ready": app_state["nltk_ready"],
            "background_init_complete": app_state["background_init_complete"],
            "errors": app_state["errors"]
        },
        "components": {
            "api": "operational",
            "websocket": "operational",
            "document_processing": "operational" if app_state["startup_complete"] else "initializing"
        }
    }

# Ready endpoint for Kubernetes-style readiness probes
@app.get("/ready")
async def ready_check():
    """Readiness probe endpoint."""
    if app_state["startup_complete"]:
        return {"status": "ready"}
    else:
        return JSONResponse(
            status_code=503,
            content={"status": "not_ready", "message": "Still initializing"}
        )

loggers.info("Application configuration completed")

# Optional: Force garbage collection to reduce memory usage
import gc
gc.collect()
